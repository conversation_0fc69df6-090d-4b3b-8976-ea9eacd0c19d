version: '3.8'

services:
  mysql:
    image: mysql:8
    container_name: mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  app:
    build: .
    container_name: microfinance-app
    depends_on:
      - mysql
    environment:
      DATABASE_URL: ${DATABASE_URL}
    ports:
      - "3000:3000"
    env_file:
      - .env

volumes:
  mysql_data:
