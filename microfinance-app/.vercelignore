# Development files
.git
.github
.vscode
.idea
.DS_Store
*.log
*.swp
*.bak

# Node.js
# Don't ignore all node_modules, just specific parts
# node_modules
node_modules/.cache
node_modules/.pnpm
node_modules/@types
node_modules/typescript
node_modules/ts-node
node_modules/eslint
node_modules/prettier
node_modules/@next/bundle-analyzer
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage
.nyc_output
test
tests
__tests__
jest.config.js

# Build artifacts
.next/cache
out
dist
build
.turbo

# Documentation
docs
*.md
LICENSE
CHANGELOG.md

# Configuration files not needed in production
.eslintrc*
.prettierrc*
.editorconfig
tsconfig.tsbuildinfo
*.config.js
*.config.ts
!next.config.js
!postcss.config.js
!tailwind.config.js

# Prisma
prisma/migrations
prisma/seed.ts
node_modules/.prisma/client/libquery_engine-*
!node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x
!node_modules/.prisma/client/libquery_engine-rhel-openssl-1.1.x
!node_modules/.prisma/client/libquery_engine-rhel-openssl-3.0.x
!node_modules/.prisma/client/libquery_engine-debian-openssl-1.0.x
!node_modules/.prisma/client/libquery_engine-debian-openssl-1.1.x
!node_modules/.prisma/client/libquery_engine-debian-openssl-3.0.x
!node_modules/.prisma/client/libquery_engine-linux-musl
node_modules/@prisma/engines/**
node_modules/prisma/libquery_engine-*
node_modules/prisma/migration-engine-*
node_modules/prisma/introspection-engine-*
node_modules/prisma/prisma-fmt-*

# Development scripts
scripts/dev-*
scripts/test-*

# Misc
.env.local
.env.development
.env.test
.env.example
