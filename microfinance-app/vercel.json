{"buildCommand": "npm run vercel-build", "installCommand": "npm run vercel-install", "framework": "nextjs", "outputDirectory": ".next", "cleanUrls": true, "trailingSlash": false, "github": {"silent": true}, "functions": {"app/api/**/*": {"memory": 1024, "maxDuration": 10}}, "env": {"NEXT_TYPESCRIPT_CHECK": "false", "NEXT_TELEMETRY_DISABLED": "1", "NODE_OPTIONS": "--max_old_space_size=3072", "SKIP_TYPESCRIPT": "1", "SKIP_ESLINT": "1", "SKIP_LINT": "1", "NEXT_IGNORE_TYPESCRIPT_ERRORS": "1", "NEXT_IGNORE_ESLINT_ERRORS": "1", "NEXT_DISABLE_SOURCEMAPS": "1"}, "build": {"env": {"NEXT_TYPESCRIPT_CHECK": "false", "NEXT_TELEMETRY_DISABLED": "1", "NODE_OPTIONS": "--max_old_space_size=3072", "SKIP_TYPESCRIPT": "1", "SKIP_ESLINT": "1", "SKIP_LINT": "1", "NEXT_IGNORE_TYPESCRIPT_ERRORS": "1", "NEXT_IGNORE_ESLINT_ERRORS": "1", "NEXT_DISABLE_SOURCEMAPS": "1"}}}