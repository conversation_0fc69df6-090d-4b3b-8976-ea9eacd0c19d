# Force npm to install optional dependencies that <PERSON><PERSON><PERSON> needs
node-linker=hoisted
public-hoist-pattern[]=*prisma*

# Optimize npm for production in Vercel
registry=https://registry.npmjs.org/
network-timeout=100000
progress=false
fund=false
audit=false

# Fix peer dependency issues
legacy-peer-deps=true
strict-peer-dependencies=false
save-exact=true
prefer-offline=true
no-fund=true
no-audit=true
no-optional=true
no-package-lock=true
ignore-scripts=false
force=true