# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Temporary folders
tmp/
temp/

# Prisma
/prisma/.env
/prisma/migrations/*_init/migration.sql

# Backup files
*.bak
*.backup
*~

# Excel temporary files
~$*.xlsx
~$*.xls

# Vercel build output
.vercel_build_output
