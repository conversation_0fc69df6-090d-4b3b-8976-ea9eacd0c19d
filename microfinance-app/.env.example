# Database connection
DATABASE_URL="mysql://username:password@host:port/database"

# Authentication configuration
JWT_SECRET="your-secret-key-for-jwt-encryption"

# Admin credentials
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="AMFadmin2020"

# Email Configuration (Gmail SMTP with App Password)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-16-character-app-password"
SMTP_FROM_NAME="Microfinance Management System"
