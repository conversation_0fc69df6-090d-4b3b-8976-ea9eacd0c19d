{"compilerOptions": {"target": "esnext", "module": "esnext", "jsx": "preserve", "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}], "noImplicitAny": false, "noImplicitThis": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noFallthroughCasesInSwitch": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "checkJs": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "react-types.d.ts", "jsx-namespace.d.ts", "global.d.ts"], "exclude": ["node_modules"]}