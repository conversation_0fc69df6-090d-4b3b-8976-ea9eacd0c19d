{"name": "microfinance-app", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "node scripts/setup-prisma.js && next build", "start": "next start", "lint": "eslint .", "format": "prettier --write .", "seed": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.ts", "analyze": "ANALYZE=true next build", "optimize": "node optimize.js", "check-app": "node scripts/check-app.js", "setup-admin": "node scripts/setup-admin.js", "verify-auth": "node scripts/verify-auth.js", "vercel-install": "npm install --legacy-peer-deps --no-audit", "vercel-build": "next build", "cleanup-prisma": "node scripts/cleanup-prisma.js", "optimize-vercel": "node scripts/optimize-vercel-output.js", "setup-default-partners": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' scripts/setup-default-partners.ts"}, "dependencies": {"@heroicons/react": "2.2.0", "@types/node-cron": "3.0.11", "@types/nodemailer": "6.4.17", "archiver": "7.0.1", "autoprefixer": "^10.0.0", "bcrypt": "^5.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "next": "latest", "node-cron": "4.0.7", "node-fetch": "2.7.0", "nodemailer": "7.0.2", "postcss": "^8.0.0", "react": "latest", "react-dom": "latest", "recharts": "^2.15.3", "source-map": "0.7.4", "swr": "^2.3.3", "tailwindcss": "^2.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@next/bundle-analyzer": "^14.1.0", "@prisma/client": "6.8.2", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "eslint": "^7.0.0", "eslint-config-next": "latest", "prettier": "^2.0.0", "prisma": "6.8.2", "ts-node": "^10.9.2", "typescript": "5.3.3"}, "engines": {"node": ">=18.0.0"}, "prisma": {"seed": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.ts"}}